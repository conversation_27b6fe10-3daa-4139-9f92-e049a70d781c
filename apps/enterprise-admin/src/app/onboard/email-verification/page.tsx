'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ArrowLeft, Mail, CheckCircle, AlertCircle } from 'lucide-react';
import { motion, Variants } from 'framer-motion';
import { Card, CardContent } from 'libs/ui/src';

export default function EmailVerificationPage() {
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [isLoading, setIsLoading] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [error, setError] = useState('');
  const [resendTimer, setResendTimer] = useState(0);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);
  const email = '<EMAIL>';

  useEffect(() => {
    if (resendTimer > 0) {
      const timer = setTimeout(() => setResendTimer(resendTimer - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [resendTimer]);

  const handleOtpChange = (index: number, value: string) => {
    if (value.length > 1) return;
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);
    setError('');
    if (value && index < 5) inputRefs.current[index + 1]?.focus();
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').slice(0, 6);
    const newOtp = [...otp];
    for (let i = 0; i < pastedData.length && i < 6; i++) {
      if (/^\d$/.test(pastedData[i])) newOtp[i] = pastedData[i];
    }
    setOtp(newOtp);
  };

  const handleVerify = async () => {
    const otpCode = otp.join('');
    if (otpCode.length !== 6) {
      setError('Please enter all 6 digits');
      return;
    }
    setIsLoading(true);
    setError('');
    try {
      await new Promise((resolve) => setTimeout(resolve, 2000));
      if (otpCode === '123456') setIsVerified(true);
      else setError('Invalid verification code. Please try again.');
    } catch {
      setError('Verification failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResend = async () => {
    setResendTimer(60);
    setError('');
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
    } catch {
      setError('Failed to resend code. Please try again.');
    }
  };

  // Framer Motion animation configs
  const sideImageVariants: Variants = {
    animateDown: {
      y: ['-1%', '240%'],
      transition: {
        repeat: Infinity,
        duration: 10,
        ease: 'linear', // ✅ "linear" string is allowed here
      },
    },
    animateUp: {
      y: ['240%', '-1%'],
      transition: {
        repeat: Infinity,
        duration: 10,
        ease: 'linear',
      },
    },
  };

  return (
    <div className="min-h-screen bg-white flex items-center justify-center p-8 relative overflow-hidden">
      {/* Left side moving image */}
      <motion.img
        src="/images/robot.png"
        alt="Side Decoration"
        className="absolute left-40 top-0 w-60 pointer-events-none filter invert hue-rotate-180"
        style={{
          filter:
            'brightness(0) saturate(100%) invert(6%) sepia(98%) saturate(7495%) hue-rotate(238deg) brightness(95%) contrast(112%)',
        }}
        variants={sideImageVariants}
        animate="animateDown"
      />

      {/* Right side moving image */}
      <motion.img
        src="/images/robot.png"
        alt="Side Decoration"
        className="absolute right-0 top-0 w-60  pointer-events-none right-40"
        style={{
          filter:
            'brightness(0) saturate(100%) invert(6%) sepia(98%) saturate(7495%) hue-rotate(238deg) brightness(95%) contrast(112%)',
        }}
        variants={sideImageVariants}
        animate="animateUp"
      />

      <div className="w-full max-w-md z-10">
        {/* Verified State */}
        {isVerified ? (
          //   <div className="bg-white rounded-2xl shadow-lg p-8 text-center border-2 border-[#37B8E6]">
          <Card className="bg-white rounded-2xl shadow-lg relative ">
            <CardContent className="p-8">
              <div className="w-16 h-16 bg-[#041C91] rounded-full flex items-center justify-center mx-auto mb-6">
                <CheckCircle className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-2xl font-bold text-[#01033D] mb-4">
                Email Verified Successfully!
              </h1>
              <p className="text-gray-400 mb-8">
                Your email has been verified. You can now continue with your
                account setup.
              </p>
              <Button
                className="w-full h-12 bg-[#041C91] hover:bg-[#01033D] text-white font-medium rounded-lg"
                onClick={() => {
                  localStorage.setItem('verifiedEmail', email);
                  window.location.href = '/onboard/company-details';
                }}
              >
                Continue
              </Button>
            </CardContent>
          </Card>
        ) : (
          <Card className="bg-white rounded-2xl shadow-lg relative ">
            <CardContent className="p-8">
              {/* Header */}
              <div className="text-center mb-8 relative">
                <Button
                  variant="ghost"
                  className="absolute top-2 left-4 p-2 text-[#01033d] hover:bg-white"
                  onClick={() => window.history.back()}
                >
                  <ArrowLeft className="w-5 h-5" />
                </Button>
                <div className="w-16 h-16 bg-[#041c91] rounded-full flex items-center justify-center mx-auto mb-6">
                  <Mail className="w-8 h-8 text-white" />
                </div>
                <h1 className="text-2xl font-bold text-[#0a0f1c] mb-2">
                  Verify Your Email
                </h1>
                <p className="text-gray-400">
                  We've sent a 6-digit verification code to
                </p>
                <p className="text-white font-medium">{email}</p>
              </div>

              {/* OTP Inputs */}
              <div className="mb-6">
                <div className="flex gap-3 justify-center mb-4">
                  {otp.map((digit, index) => (
                    <Input
                      key={index}
                      // ref={(el) => (inputRefs.current[index] = el)}
                      ref={(el) => {
                        inputRefs.current[index] = el;
                      }}
                      type="text"
                      inputMode="numeric"
                      maxLength={1}
                      value={digit}
                      onChange={(e) => handleOtpChange(index, e.target.value)}
                      onKeyDown={(e) => handleKeyDown(index, e)}
                      onPaste={handlePaste}
                      className={`w-12 h-12 text-center text-lg font-semibold border-2 rounded-lg transition-all text-[#0a0f1c] bg-white ${
                        error
                          ? 'border-red-400 focus:border-red-500'
                          : 'border-[#041c91] focus:border-[#041C91]'
                      }`}
                    />
                  ))}
                </div>

                {error && (
                  <div className="flex items-center gap-2 text-red-400 text-sm">
                    <AlertCircle className="w-4 h-4" />
                    {error}
                  </div>
                )}
              </div>

              {/* Verify Button */}
              <Button
                onClick={handleVerify}
                disabled={isLoading || otp.join('').length !== 6}
                className="w-full h-12 bg-[#041c91] hover:bg-[#01033D] disabled:bg-gray-700 text-white font-medium rounded-lg transition-colors mb-6"
              >
                {isLoading ? 'Verifying...' : 'Verify Email'}
              </Button>

              {/* Resend */}
              <div className="text-center">
                <p className="text-gray-400 text-sm mb-2">
                  Didn't receive the code?
                </p>
                <Button
                  variant="link"
                  onClick={handleResend}
                  disabled={resendTimer > 0}
                  className="text-[#041C91] hover:text-[#01033d] p-0 h-auto font-medium"
                >
                  {resendTimer > 0
                    ? `Resend in ${resendTimer}s`
                    : 'Resend verification code'}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
