/* eslint-disable camelcase */
import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { RootState } from '../../redux/store';

export interface AuthData {
  accessToken: string | null;
  refreshToken: string | null;
  expires: number | null;
}

export interface AuthResData {
  accessToken: string;
  expires: number;
  refreshToken: string;
  token_type?: string;
  'not-before-policy'?: number;
  session_state?: string;
  scope?: string;
}

export interface AuthState extends AuthData {
  isLoggedIn: boolean;
}

const initialState: AuthState = {
  accessToken:
    typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null,
  refreshToken:
    typeof window !== 'undefined' ? localStorage.getItem('refreshToken') : null,
  expires: null,
  isLoggedIn:
    typeof window !== 'undefined'
      ? !!localStorage.getItem('accessToken')
      : false,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setCredentials: (state, action: PayloadAction<AuthResData>) => {
      const { accessToken, refreshToken, expires } = action.payload;
      state.accessToken = accessToken;
      state.refreshToken = refreshToken;
      state.expires = expires;
      state.isLoggedIn = true;
    },
    unsetCredentials: (state) => {
      state.accessToken = null;
      state.refreshToken = null;
      state.expires = null;
      state.isLoggedIn = false;
    },
  },
});

export const { setCredentials, unsetCredentials } = authSlice.actions;

export default authSlice.reducer;

// Selectors
export const selectCurrentLoginStatus = (state: RootState) =>
  state.auth.isLoggedIn;
export const selectCurrentAccessToken = (state: RootState) =>
  state.auth.accessToken;
export const selectCurrentRefreshToken = (state: RootState) =>
  state.auth.refreshToken;
export const selectCurrentAuthState = (state: RootState) => state.auth;
