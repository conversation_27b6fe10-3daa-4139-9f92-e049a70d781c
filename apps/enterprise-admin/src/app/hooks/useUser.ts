import { useGetUserQuery } from 'redux/auth/authApiSlice';
import { useAppSelector } from 'redux/hooks';

interface UserProps {
  name: string;
  email: string;
  avatar: string;
  thumb: string;
  role: string;
}

export default function useUser() {
  const isLoggedIn = useAppSelector((state) => state.auth.isLoggedIn);
  const { data: user } = useGetUserQuery(undefined, { skip: !isLoggedIn });

  if (user) {
    const thumb = user?.photoUrl ?? '/assets/images/users/avatar-thumb-1.png';

    const newUser: UserProps = {
      name: user!.firstName ? `${user.firstName} ${user.lastName}` : '',
      email: user!.email!,
      avatar: user?.photoUrl ?? '/assets/images/users/avatar-1.png',
      thumb,
      role: user.role
    };

    return newUser;
  }
  return false;
}
