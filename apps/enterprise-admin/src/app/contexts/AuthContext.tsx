import { PermissionKeys } from 'enums/permission-keys.enum';
import { createContext, useContext, ReactNode } from 'react';
import { useGetUserQuery } from 'redux/auth/authApiSlice';
import { useAppSelector } from 'redux/hooks';
import { User } from 'types/user-profile';

interface AuthContextType {
  user: User;
  hasPermission: (permission: PermissionKeys) => boolean;
}

const AuthContext = createContext<AuthContextType | null>(null);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const isLoggedIn = useAppSelector((state) => state.auth.isLoggedIn);
  const { data: currentUser } = useGetUserQuery(undefined, { skip: !isLoggedIn });
  const user = currentUser!;
  const hasPermission = (permission: PermissionKeys) => {
    return user?.role === 'Admin' || user?.permissions.includes(permission);
  };

  return <AuthContext.Provider value={{ user, hasPermission }}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
