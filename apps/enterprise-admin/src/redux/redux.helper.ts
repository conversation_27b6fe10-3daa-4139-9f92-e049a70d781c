import { ApiSliceIdentifier } from '../enums/api.enum';
import { RootState } from './store';

export function getBaseUrl(
  state: RootState,
  apiSliceIdentifier?: ApiSliceIdentifier
) {
  if (!apiSliceIdentifier) return process.env.NEXT_PUBLIC_AUTH_SERVICE_URL;
  const baseUrlMap: Partial<Record<ApiSliceIdentifier, string | undefined>> = {
    [ApiSliceIdentifier.AUTH_SERVICE]: process.env.NEXT_PUBLIC_AUTH_SERVICE_URL,
    [ApiSliceIdentifier.ECOM_SERVICE]:
      process.env.NEXT_PUBLIC_ECOM_API_SERVICE_URL,
    [ApiSliceIdentifier.NOTIFICATION_SERVICE]:
      process.env.NEXT_PUBLIC_NOTIFICATION_API_SERVICE_URL,
    [ApiSliceIdentifier.ECOM_FACADE]: process.env.NEXT_PUBLIC_ECOM_FACADE_URL,
  };

  return baseUrlMap[apiSliceIdentifier!];
}
