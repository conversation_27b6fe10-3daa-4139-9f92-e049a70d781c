{"name": "@b6-ai-ui/enterprise-admin", "version": "0.0.1", "private": true, "scripts": {"build": "next build", "dev": "next dev", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "clean": "rm -rf .next"}, "dependencies": {"@module-federation/nextjs-mf": "^8.8.39", "@reduxjs/toolkit": "^2.9.0", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "next": "~15.2.4", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.62.0", "react-redux": "^9.2.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"tsconfig-paths-webpack-plugin": "^4.2.0"}}